# CSS Architecture Documentation

## Overview

This project implements a modular CSS architecture that separates styles between public/unauthenticated pages and authenticated app pages, while maintaining a shared design system foundation.

## Architecture Structure

```
src/
├── app.css                           # Base entry point (shared foundation)
├── app-public.css                    # Public/marketing pages entry point
├── app-authenticated.css             # Authenticated app pages entry point
└── styles/
    ├── base/
    │   ├── variables.css             # CSS custom properties and design tokens
    │   └── typography.css            # Typography system and font definitions
    ├── components/
    │   ├── design-system.css         # Shared component styles
    │   └── animations.css            # Shared animation system
    ├── themes/
    │   └── theme-variables.css       # Theme integration for Tailwind
    ├── public/
    │   ├── marketing.css             # Marketing-specific styles
    │   ├── landing-pages.css         # Landing page components
    │   └── auth-forms.css            # Authentication form styles
    └── app/
        ├── dashboard.css             # Dashboard-specific styles
        ├── sidebar.css               # Sidebar and navigation styles
        └── data-tables.css           # Data visualization and tables
```

## Entry Points

### 1. Base Entry Point (`app.css`)
- Loaded by the root layout (`src/routes/+layout.svelte`)
- Contains shared foundation styles available to all sections
- Imports: base styles, shared components, animations, theme variables

### 2. Public Entry Point (`app-public.css`)
- Loaded by marketing layout (`src/routes/(marketing)/+layout.svelte`)
- Contains styles specific to public/unauthenticated pages
- Includes: marketing components, landing pages, auth forms

### 3. Authenticated Entry Point (`app-authenticated.css`)
- Loaded by admin layout (`src/routes/(admin)/dashboard/[envSlug]/(menu)/+layout.svelte`)
- Contains styles specific to authenticated app pages
- Includes: dashboard, sidebar, data tables, admin components

## Design System Foundation

### Variables (`base/variables.css`)
- CSS custom properties for colors, spacing, typography
- Design tokens that can be used across all sections
- Maintains consistency while allowing section-specific overrides

### Typography (`base/typography.css`)
- Font family definitions (Plus Jakarta Sans, serif, monospace stacks)
- Typography scale and hierarchy
- Text utilities and classes

### Shared Components (`components/design-system.css`)
- Button styles and variants
- Form elements and inputs
- Card components
- Modal and dialog styles
- Navigation components

### Animations (`components/animations.css`)
- Motion One integration support
- Page transition animations
- Micro-interactions and hover effects
- Accessibility-aware animations (respects prefers-reduced-motion)

## Section-Specific Styles

### Public Section (`styles/public/`)

#### Marketing (`marketing.css`)
- Hero sections and CTAs
- Feature showcases
- Testimonials and social proof
- Pricing tables

#### Landing Pages (`landing-pages.css`)
- Page-specific layouts
- Conversion-focused components
- A/B testing variants

#### Auth Forms (`auth-forms.css`)
- Login and signup forms
- Password reset flows
- Email verification pages

### Authenticated Section (`styles/app/`)

#### Dashboard (`dashboard.css`)
- Dashboard layouts and grids
- Widget containers
- Metric displays
- Quick actions

#### Sidebar (`sidebar.css`)
- Navigation sidebar styles
- Collapsible states
- Menu items and icons
- User profile sections

#### Data Tables (`data-tables.css`)
- Table layouts and styling
- Data visualization components
- Filtering and search interfaces
- Export and action buttons

## Usage Guidelines

### Adding New Styles

1. **Determine the appropriate section**: Public, authenticated, or shared
2. **Choose the correct file**: Based on component type and usage
3. **Follow naming conventions**: Use semantic class names
4. **Maintain consistency**: Use design tokens from variables.css

### Modifying Existing Styles

1. **Identify the scope**: Check if changes affect one section or multiple
2. **Update the appropriate file**: Don't mix section-specific styles
3. **Test across sections**: Ensure changes don't break other areas
4. **Update documentation**: Keep this README current

### Performance Considerations

- **Code splitting**: Each section loads only its required styles
- **Shared foundation**: Common styles are loaded once at the root
- **Lazy loading**: Section-specific styles load only when needed
- **Build optimization**: Vite automatically optimizes and bundles CSS

## Development Workflow

### Local Development
```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview
```

### CSS Architecture Benefits

1. **Separation of Concerns**: Public and authenticated styles are isolated
2. **Maintainability**: Easy to locate and modify section-specific styles
3. **Performance**: Optimized loading with code splitting
4. **Scalability**: Easy to add new sections or components
5. **Consistency**: Shared design system ensures visual coherence

### Migration Notes

- Original `app.css` content has been distributed across the new architecture
- All existing functionality is preserved
- Build process automatically handles the new structure
- No changes required to existing component files

## Troubleshooting

### Common Issues

1. **Styles not loading**: Check import paths in layout files
2. **Conflicting styles**: Ensure proper CSS specificity and order
3. **Missing variables**: Verify design tokens are imported correctly
4. **Build errors**: Check for CSS syntax errors in individual files

### Debug Tips

- Use browser dev tools to inspect which CSS files are loaded
- Check the build output for CSS file sizes and content
- Verify import order in entry point files
- Test with production builds to catch optimization issues
