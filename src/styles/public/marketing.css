/* Marketing/Public Pages Specific Styles */

/* Warm Theme Typography */
.linear-heading {
  font-family: var(--font-sans);
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.linear-body {
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.linear-mono {
  font-family: var(--font-mono);
  font-weight: 500;
  letter-spacing: -0.01em;
}

/* Navigation - Warm Style */
.linear-nav {
  background: rgba(248, 244, 238, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

/* Navigation blur variant */
.nav-blur {
  background: rgba(248, 244, 238, 0.9);
  border-bottom: 2px solid var(--border);
  backdrop-filter: blur(10px);
}

/* Mobile menu base styles */
.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background);
  z-index: 100;
  padding: 1.5rem;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Cards - Warm Inspired */
.linear-card {
  background: var(--card);
  border: 1px solid var(--border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-card:hover {
  border-color: var(--primary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Buttons - Warm Theme */
.linear-btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--primary);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-btn-primary:hover {
  background: hsl(15.1111 55.5556% 48%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.linear-btn-secondary {
  background: transparent;
  color: var(--foreground);
  border: 1px solid var(--border);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

.linear-btn-secondary:hover {
  background: var(--muted);
  border-color: var(--primary);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

/* Tags - Warm Color Usage */
.linear-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

/* Green - Primary actions and success */
.linear-tag-green {
  background: hsl(120 30% 90%);
  border-color: hsl(120 40% 75%);
  color: hsl(120 60% 25%);
}

.linear-tag-green:hover {
  background: hsl(120 35% 85%);
  border-color: hsl(120 50% 65%);
  box-shadow: var(--shadow);
}

/* Purple - Premium features */
.linear-tag-purple {
  background: hsl(270 40% 90%);
  border-color: hsl(270 50% 75%);
  color: hsl(270 70% 30%);
}

/* Blue - Technical features (used sparingly) */
.linear-tag-blue {
  background: hsl(220 40% 90%);
  border-color: hsl(220 50% 75%);
  color: hsl(220 70% 30%);
}

/* Orange - Results and metrics (used sparingly) */
.linear-tag-orange {
  background: hsl(25 40% 90%);
  border-color: hsl(25 50% 75%);
  color: hsl(25 70% 30%);
}

/* Pink - Case studies (used very sparingly) */
.linear-tag-pink {
  background: hsl(330 40% 90%);
  border-color: hsl(330 50% 75%);
  color: hsl(330 70% 30%);
}

/* Data Visualization - Warm */
.linear-chart {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.linear-metric {
  font-family: var(--font-mono);
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--primary);
}

/* Thin Lines & Engineering Aesthetic - Warm */
/* Thin Lines & Engineering Aesthetic - Warm */
.linear-grid {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.04) 1px, transparent 1px);
  background-size: 24px 24px;
}

.linear-divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%
  );
  margin: 3rem 0;
}

.linear-dotted-line {
  border-top: 1px dotted rgba(0, 0, 0, 0.15);
  margin: 1.5rem 0;
}

/* Team Cards - Warm */
.linear-team-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-team-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Testimonials - Warm */
.linear-testimonial {
  background: var(--card);
  border-left: 2px solid var(--primary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  position: relative;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.linear-testimonial::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary) 0%, hsl(15 55% 45%) 100%);
  box-shadow: 0 0 8px rgba(218, 136, 103, 0.3);
}

/* Enhanced sticky navigation */
.nav-hidden {
  transform: translateY(-100%);
}

/* Navigation link hover effects */
.nav-link {
  position: relative;
  padding: 0.25rem 0;
  color: var(--foreground);
  background: none;
  border: none;
  cursor: pointer;
}

/* Logo button styling */
.linear-heading button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  text-align: left;
}

/* Dashboard mockup styles */
.dashboard-card {
  background: var(--card);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-lg);
  border-radius: 0.5rem; /* 8px */
}

.metric-card {
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
}

.metric-card:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px 0px hsl(0 0% 0% / 1);
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: var(--primary);
  transition:
    width 0.3s ease,
    left 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
  left: 0;
}

.nav-link:hover {
  color: var(--primary);
}
